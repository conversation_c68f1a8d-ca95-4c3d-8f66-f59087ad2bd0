---
description: Explore the Ultralytics YOLO OBB Trainer class for efficient training with Oriented Bounding Box models. Learn with examples and method details.
keywords: Ultralytics, YOLO, OBB Trainer, Oriented Bounding Box, Machine Learning, Training, AI
---

# Reference for `ultralytics/models/yolo/obb/train.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/obb/train.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/obb/train.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/obb/train.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.yolo.obb.train.OBBTrainer

<br><br>
