#include "activationwindow.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMessageBox>
#include <QDebug>
#include <QString>

// 条件编译：根据编译模式包含不同的头文件
#ifdef PRODUCTION_LICENSE
    #include "SFCoreIntf.h"
    #include "YXPermission.h"
#endif

ActivationWindow::ActivationWindow(QWidget *parent)
    : QWidget(parent)
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);

    QLabel *label = new QLabel(tr("请输入激活码："), this);
    licenseEdit = new QLineEdit(this);
    activateButton = new QPushButton(tr("激活"), this);
    statusLabel = new QLabel(this);

    statusLabel->setAlignment(Qt::AlignCenter);

    mainLayout->addWidget(label);
    mainLayout->addWidget(licenseEdit);
    mainLayout->addWidget(activateButton);
    mainLayout->addWidget(statusLabel);

    connect(activateButton, &QPushButton::clicked, this, &ActivationWindow::onActivateClicked);
}

void ActivationWindow::onActivateClicked()
{
    QString qLic = licenseEdit->text().trimmed();
    if (qLic.isEmpty()) {
        QMessageBox::warning(this, tr("错误"), tr("请输入激活码！"));
        return;
    }

#ifdef PRODUCTION_LICENSE
    // 生产模式：使用完整的许可证验证
    qDebug() << "[激活] 使用生产模式许可证验证";

    // 获取真实设备号
    std::string deviceNo = ::getDeviceNo();
    qDebug() << "[激活] 当前设备号:" << QString::fromStdString(deviceNo);

    // 构造激活参数
    DeviceVM vm;
    vm.deviceNo = deviceNo;  // 使用真实设备号
    vm.productNo = "40201";
    vm.licenseNo = qLic.toStdString();

    // 调用服务端激活接口
    SF_LICENSE_STATE state = callServerToActivate(vm);
    std::string errMsg = getErrorMessage(state);

    if (state == SF_OK) {
        statusLabel->setText(tr("激活成功，正在打开转换窗口..."));
        convWindow = new ConversionWindow();
        convWindow->show();
        this->close();
    } else {
        statusLabel->setText(QString::fromLocal8Bit(errMsg.c_str()));
        QMessageBox::warning(this, tr("激活失败"), QString::fromLocal8Bit(errMsg.c_str()));
    }

#else
    // 开发模式：使用简化验证（兼容你的开发环境）
    qDebug() << "[激活] 使用开发模式简化验证";

    // 模拟设备号（用于开发调试）
    QString deviceNo = "50bf70582c5ea2ac7502656f8cfb522e";
    qDebug() << "[激活] 模拟设备号:" << deviceNo;
    qDebug() << "[激活] 注意：这是开发模式，生产环境请使用 CONFIG+=production 编译";

    // 简化的激活验证（仅用于开发）
    if (qLic.length() >= 10 && qLic.contains("-")) {
        statusLabel->setText(tr("激活成功（开发模式），正在打开转换窗口..."));
        convWindow = new ConversionWindow();
        convWindow->show();
        this->close();
    } else {
        statusLabel->setText(tr("激活失败: 请输入有效的激活码（开发模式：需要10位以上且包含'-'）"));
        QMessageBox::warning(this, tr("激活失败"), tr("请输入有效的激活码"));
    }
#endif
}
