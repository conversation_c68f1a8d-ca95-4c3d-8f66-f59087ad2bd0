#!/usr/bin/env bash
# convert_model.sh: YOLO模型转换简化脚本

set -euo pipefail

# 输出样式
RED='\033[0;31m'; GREEN='\033[0;32m'; YELLOW='\033[0;33m'; CYAN='\033[0;36m'; NC='\033[0m'
error() { echo -e "${RED}[ERROR]${NC} $1" >&2; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
info() { echo -e "${CYAN}[INFO]${NC} $1"; }
progress() {
    echo -en "${YELLOW}[PROGRESS]${NC} $2 "
    while kill -0 $1 2>/dev/null; do for c in / - \\ \|; do printf "\b$c"; sleep 0.1; done; done
    printf "\b "
}

# 参数解析
MODEL="yolov5"
WEIGHT_PATH=""
DATASET_PATH="datasets/COCO"
OUTPUT_DIR="result"

while [[ $# -gt 0 ]]; do
    case "$1" in
        --model) MODEL="$2"; shift 2;;
        --weight) WEIGHT_PATH="$2"; shift 2;;
        --dataset) DATASET_PATH="$2"; shift 2;;
        --output) OUTPUT_DIR="$2"; shift 2;;
        -h|--help)
            echo "用法: $0 --weight yolov5s.pt [--model yolov5|yolov8] [--output 目录]"; exit 0;;
        *) error "未知参数: $1"; exit 1;;
    esac
done

[[ -z "$WEIGHT_PATH" ]] && { error "必须指定 --weight"; exit 1; }

SCRIPT_DIR=$(cd "$(dirname "$0")" && pwd)

convert_model() {
    local BUILD_MODEL_DIR LOG_FILE

    case "$MODEL" in
        yolov5)
            BUILD_MODEL_DIR="yolov5"
            EXPORT_CMD=("python ${SCRIPT_DIR}/yolov5/export.py --rknpu --weight ${WEIGHT_PATH} --result ${SCRIPT_DIR}/${OUTPUT_DIR}/${BUILD_MODEL_DIR}/yolov5.onnx")
            CONVERT_CMD=("python ${SCRIPT_DIR}/yolov5/convert.py ${SCRIPT_DIR}/${OUTPUT_DIR}/${BUILD_MODEL_DIR}/yolov5.onnx rk3588 i8 ${SCRIPT_DIR}/${OUTPUT_DIR}/${BUILD_MODEL_DIR}/yolov5.rknn ${DATASET_PATH}/coco_subset_20.txt")
            ;;
        yolov8)
            BUILD_MODEL_DIR="yolov8"
            export PYTHONPATH="${PYTHONPATH:-}:$(pwd)/yolov8"
            EXPORT_CMD=("python ${SCRIPT_DIR}/yolov8/ultralytics/engine/exporter.py --weight ${WEIGHT_PATH} --result ${SCRIPT_DIR}/${OUTPUT_DIR}/${BUILD_MODEL_DIR}/yolov8.onnx")
            CONVERT_CMD=("python ${SCRIPT_DIR}/yolov8/convert.py ${SCRIPT_DIR}/${OUTPUT_DIR}/${BUILD_MODEL_DIR}/yolov8.onnx rk3588 i8 ${SCRIPT_DIR}/${OUTPUT_DIR}/${BUILD_MODEL_DIR}/yolov8.rknn ${DATASET_PATH}/coco_subset_20.txt")
            ;;
        *) error "不支持的模型类型: $MODEL"; exit 1;;
    esac

    mkdir -p "${SCRIPT_DIR}/${OUTPUT_DIR}/${BUILD_MODEL_DIR}"

    # 导出 ONNX
    info "导出 ONNX 模型..."
    LOG_FILE="${SCRIPT_DIR}/${OUTPUT_DIR}/export_onnx.log"
    eval "${EXPORT_CMD[@]}" > "$LOG_FILE" 2>&1 &
    progress $! "导出ONNX"
    [[ $? -ne 0 ]] && { error "ONNX 导出失败"; exit 1; }

    # 转换 RKNN
    info "转换 RKNN 模型..."
    LOG_FILE="${SCRIPT_DIR}/${OUTPUT_DIR}/convert_rknn.log"
    eval "${CONVERT_CMD[@]}" > "$LOG_FILE" 2>&1 &
    progress $! "转换RKNN"
    [[ $? -ne 0 ]] && { error "RKNN 转换失败"; exit 1; }

    # 编译部署包
    LOG_FILE="${SCRIPT_DIR}/${OUTPUT_DIR}/build.log"
    info "编译部署包..."
    (export GCC_COMPILER=$(which aarch64-linux-gnu-gcc);
     bash ${SCRIPT_DIR}/build-linux.sh -t rk3588 -a aarch64 -d ${BUILD_MODEL_DIR} \
        -i ${SCRIPT_DIR}/${OUTPUT_DIR}/${BUILD_MODEL_DIR} \
        -o ${SCRIPT_DIR}/${OUTPUT_DIR}/${BUILD_MODEL_DIR}/deploy > "$LOG_FILE" 2>&1) &
    progress $! "编译部署包"
    [[ $? -ne 0 ]] && { error "部署包编译失败"; exit 1; }

    success "模型转换完成！"
    echo -e "✅ RKNN模型: ${CYAN}${SCRIPT_DIR}/${OUTPUT_DIR}/${BUILD_MODEL_DIR}/${BUILD_MODEL_DIR}.rknn${NC}"
    echo -e "✅ 部署包目录: ${CYAN}${SCRIPT_DIR}/${OUTPUT_DIR}/${BUILD_MODEL_DIR}/deploy${NC}"
}

convert_model
