#!/usr/bin/env bash
set -euo pipefail

# 用户可根据需要修改
DOCKERHUB_USER="sunruihanshifang"
IMAGE_NAME="yolo-rknn"
TAG="latest"

# 构建
echo "==> 构建镜像 ${DOCKERHUB_USER}/${IMAGE_NAME}:${TAG}"
docker build -t ${DOCKERHUB_USER}/${IMAGE_NAME}:${TAG} .

# 登录（如果已经登录可跳过）
echo "==> 登录 Docker Hub"
docker login --username ${DOCKERHUB_USER}

# 推送
echo "==> 推送到 Docker Hub"
docker push ${DOCKERHUB_USER}/${IMAGE_NAME}:${TAG}

echo "✅ 完成：镜像已推送为 ${DOCKERHUB_USER}/${IMAGE_NAME}:${TAG}"
