# Qt应用程序许可证兼容解决方案

## 🎯 解决的问题

原始问题：Qt版本不匹配（你的5.12.8 vs 原始要求5.12.6）导致许可证验证编译失败，被迫移除安全验证。

## 💡 解决方案

采用**条件编译**技术，实现：
- ✅ **开发模式**：兼容你的Qt 5.12.8环境，使用简化验证
- ✅ **生产模式**：使用完整许可证验证，保证部署安全
- ✅ **无缝切换**：一套代码，两种模式，智能编译

## 🚀 快速开始

### 1. 测试兼容性
```bash
./test_license_modes.sh
```

### 2. 开发模式编译（推荐用于你的环境）
```bash
./build_qt_app.sh dev
```

### 3. 运行应用程序
```bash
./terminal_command.sh
```

### 4. 生产模式编译（用于设备部署）
```bash
./build_qt_app.sh production
```

## 📋 编译模式对比

| 特性 | 开发模式 | 生产模式 |
|------|----------|----------|
| Qt版本要求 | 5.12.x（兼容系统版本） | 5.12.6（推荐） |
| 许可证验证 | 简化验证 | 完整网络验证 |
| 安全级别 | 低（仅开发用） | 高（生产级别） |
| 网络要求 | 无 | 需要连接许可证服务器 |
| 激活要求 | 10位+包含"-" | 真实许可证码 |
| 编译依赖 | 仅Qt基础库 | Qt+许可证库 |

## 🔧 使用方法

### 开发环境（你的日常使用）
```bash
# 编译开发版本
./build_qt_app.sh dev

# 运行程序
./terminal_command.sh

# 测试激活码
E76G-JEQR-EQRA-T7ZW
```

### 生产环境（设备部署）
```bash
# 确保许可证库文件存在
ls autobit/ModelConverter/libsf-core-ls.so

# 编译生产版本
./build_qt_app.sh production

# 部署到设备
tar -czf ModelConverter-production.tar.gz autobit/ModelConverter/build/
```

## 🛡️ 安全性说明

### 开发模式安全特性
- ⚠️ **仅用于开发调试**
- ⚠️ **使用模拟设备号**
- ⚠️ **简化的激活验证**
- ✅ **明确标识为开发模式**

### 生产模式安全特性
- ✅ **真实设备指纹识别**
- ✅ **网络许可证服务器验证**
- ✅ **加密设备信息传输**
- ✅ **完整错误处理**

## 📁 文件结构

```
├── build_qt_app.sh              # 智能编译脚本
├── test_license_modes.sh        # 兼容性测试脚本
├── deploy_config.json           # 部署配置文件
├── terminal_command.sh          # 更新的启动脚本
└── autobit/ModelConverter/
    ├── ModelConverter.pro       # 支持条件编译的项目文件
    ├── activationwindow.cpp     # 支持双模式的激活窗口
    ├── libsf-core-ls.so        # 许可证库（生产模式需要）
    ├── SFCoreIntf.h            # 许可证接口（生产模式需要）
    └── YXPermission.h          # 许可证权限（生产模式需要）
```

## 🔍 技术实现

### 条件编译宏
- `DEV_MODE_LICENSE`: 开发模式标识
- `PRODUCTION_LICENSE`: 生产模式标识

### 编译配置
```pro
dev_mode {
    DEFINES += DEV_MODE_LICENSE
} else:production {
    DEFINES += SF_CORE_LS_EXPORT=Q_DECL_IMPORT
    DEFINES += PRODUCTION_LICENSE
    LIBS += -L$$PWD -lsf-core-ls
}
```

### 代码示例
```cpp
#ifdef PRODUCTION_LICENSE
    // 生产模式：完整许可证验证
    SF_LICENSE_STATE state = callServerToActivate(vm);
#else
    // 开发模式：简化验证
    if (qLic.length() >= 10 && qLic.contains("-")) {
        // 激活成功
    }
#endif
```

## ❓ 常见问题

### Q: 我现在还能正常开发吗？
A: 是的！默认使用开发模式，完全兼容你的Qt 5.12.8环境。

### Q: 部署到设备时安全吗？
A: 使用生产模式编译，安全性与原始版本相同。

### Q: 如何切换模式？
A: 使用编译脚本参数：`./build_qt_app.sh dev` 或 `./build_qt_app.sh production`

### Q: 开发模式有什么限制？
A: 仅用于开发调试，激活验证简化，不可用于生产环境。

## 🎉 优势总结

1. **兼容性**：解决Qt版本不匹配问题
2. **安全性**：生产模式保持完整安全验证
3. **便利性**：开发模式简化流程
4. **灵活性**：一套代码支持两种场景
5. **可维护性**：清晰的模式区分和文档
