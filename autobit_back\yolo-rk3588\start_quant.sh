#!/bin/bash

# 解析命名参数
while [[ $# -gt 0 ]]; do
    case "$1" in
        --model_name)
        model_name="$2"
        shift 2
        ;;
        --model_weight_path)
        model_weight_path="$2"
        shift 2
        ;;
        --model_py_path)
        model_py_path="$2"
        shift 2
        ;;
        --dataset_path)
        dataset_path="$2"
        shift 2
        ;;
        --result_model_path)
        result_model_path="$2"
        shift 2
        ;;
        *)
        echo "未知参数: $1"
        exit 1
        ;;
    esac
done

# 检查必需参数是否存在
if [ -z "$model_name" ] || [ -z "$model_weight_path" ] || [ -z "$model_py_path" ] || [ -z "$dataset_path" ] || [ -z "$result_model_path" ]; then
    echo "错误：缺少必需参数！"
    echo "必需参数：--model_name --model_weight_path --model_py_path --dataset_path --result_model_path"
    exit 1
fi

# 根据模型名称选择对应的脚本路径
case "$model_name" in
    yolov5*)
        model_type="yolov5"
        export_script="yolov5/export.py"
        convert_script="yolov5/convert.py"
        ;;
    yolov8*)
        model_type="yolov8"
        export_script="yolov8/ultralytics/engine/exporter.py"
        convert_script="yolov8/convert.py"
        export PYTHONPATH=./yolov8
        ;;
    *)
        echo "错误：不支持的模型名称：$model_name, 仅支持 yolov5 或 yolov8"
        exit 1
        ;;
esac

# 模型转换与量化
# echo "正在执行模型导出（$model_name)..."
python "$export_script" --rknpu --weight "$model_weight_path" --result "${result_model_path}/${model_name}.onnx"

# echo "正在执行模型转换（$model_name)..."
python "$convert_script" "${result_model_path}/${model_name}.onnx" rk3588 i8 "${result_model_path}/${model_name}.rknn" "${dataset_path}/coco_subset_20.txt"

# 模型编译
# echo "正在编译模型..."
./build-linux.sh -t rk3588 -a aarch64 -d "$model_type" -i "${result_model_path}" -o "${result_model_path}/deploy"

echo "任务完成！结果保存在：${result_model_path}/deploy"