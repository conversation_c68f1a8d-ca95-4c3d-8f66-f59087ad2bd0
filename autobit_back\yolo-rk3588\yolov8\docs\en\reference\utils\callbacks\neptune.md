---
description: Learn how to use NeptuneAI with Ultralytics for advanced logging and tracking of experiments. Detailed setup and callback functions included.
keywords: Ultralytics, NeptuneAI, YOLO, experiment logging, machine learning, AI, callbacks, training, validation
---

# Reference for `ultralytics/utils/callbacks/neptune.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/neptune.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/neptune.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/callbacks/neptune.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.callbacks.neptune._log_scalars

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.neptune._log_images

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.neptune._log_plot

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.neptune.on_pretrain_routine_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.neptune.on_train_epoch_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.neptune.on_fit_epoch_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.neptune.on_val_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.neptune.on_train_end

<br><br>
