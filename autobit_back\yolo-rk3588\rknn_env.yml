name: RKNN-Toolkit2
channels:
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - ca-certificates=2025.2.25=h06a4308_0
  - ld_impl_linux-64=2.40=h12ee557_0
  - libffi=3.3=he6710b0_2
  - libgcc-ng=9.1.0=hdf63c60_0
  - libstdcxx-ng=9.1.0=hdf63c60_0
  - ncurses=6.3=h7f8727e_2
  - openssl=1.1.1w=h7f8727e_0
  - pip=24.2=py38h06a4308_0
  - python=3.8.13=h12debd9_0
  - readline=8.1.2=h7f8727e_1
  - setuptools=75.1.0=py38h06a4308_0
  - sqlite=3.38.5=hc218d9a_0
  - tk=8.6.12=h1ccaba5_0
  - wheel=0.44.0=py38h06a4308_0
  - xz=5.2.5=h7f8727e_1
  - zlib=1.2.12=h7f8727e_2
  - pip:
    - absl-py==2.2.2
    - addict==2.4.0
    - asttokens==3.0.0
    - backcall==0.2.0
    - cachetools==5.5.2
    - certifi==2025.4.26
    - charset-normalizer==3.4.2
    - cloudpickle==3.1.1
    - coloredlogs==15.0.1
    - contourpy==1.1.1
    - cycler==0.12.1
    - cython==3.1.1
    - decorator==5.2.1
    - easydict==1.13
    - executing==2.2.0
    - fast-histogram==0.13
    - filelock==3.16.1
    - flatbuffers==25.2.10
    - fonttools==4.57.0
    - fsspec==2025.3.0
    - gitdb==4.0.12
    - gitpython==3.1.44
    - google-auth==2.40.2
    - google-auth-oauthlib==1.0.0
    - grpcio==1.70.0
    - humanfriendly==10.0
    - idna==3.10
    - importlib_metadata==8.5.0
    - importlib_resources==6.4.5
    - ipython==8.12.3
    - jedi==0.19.2
    - jinja2==3.1.6
    - kiwisolver==1.4.7
    - markdown==3.7
    - markupsafe==2.1.5
    - matplotlib==3.7.5
    - matplotlib-inline==0.1.7
    - mpmath==1.3.0
    - networkx==3.1
    - numpy==1.24.4
    - nvidia-cublas-cu12==********
    - nvidia-cuda-cupti-cu12==12.1.105
    - nvidia-cuda-nvrtc-cu12==12.1.105
    - nvidia-cuda-runtime-cu12==12.1.105
    - nvidia-cudnn-cu12==********
    - nvidia-cufft-cu12==*********
    - nvidia-curand-cu12==**********
    - nvidia-cusolver-cu12==**********
    - nvidia-cusparse-cu12==**********
    - nvidia-nccl-cu12==2.20.5
    - nvidia-nvjitlink-cu12==12.9.41
    - nvidia-nvtx-cu12==12.1.105
    - oauthlib==3.2.2
    - onnx==1.17.0
    - onnxruntime==1.19.2
    - opencv-python==*********
    - packaging==25.0
    - pandas==2.0.3
    - panopticapi==0.1
    - parso==0.8.4
    - pexpect==4.9.0
    - pickleshare==0.7.5
    - pillow==10.4.0
    - prompt_toolkit==3.0.51
    - protobuf==4.25.4
    - psutil==7.0.0
    - ptyprocess==0.7.0
    - pure_eval==0.2.3
    - pyasn1==0.6.1
    - pyasn1_modules==0.4.2
    - pycocotools==2.0.7
    - pygments==2.19.1
    - pyparsing==3.1.4
    - python-dateutil==2.9.0.post0
    - pytz==2025.2
    - pyyaml==6.0.2
    - requests==2.32.3
    - requests-oauthlib==2.0.0
    - rknn-toolkit2==2.3.2
    - rsa==4.9.1
    - ruamel.yaml==0.18.10
    - ruamel.yaml.clib==0.2.8
    - scipy==1.10.1
    - seaborn==0.13.2
    - sentry-sdk==2.29.1
    - six==1.17.0
    - smmap==5.0.2
    - stack-data==0.6.3
    - submitit==1.5.3
    - sympy==1.13.3
    - tensorboard==2.14.0
    - tensorboard-data-server==0.7.2
    - termcolor==2.4.0
    - thop==0.1.1.post2209072238
    - timm==0.4.12
    - torch==1.10.1
    - torch-pruning==1.5.1
    - torchvision==0.11.2
    - tqdm==4.67.1
    - traitlets==5.14.3
    - triton==3.0.0
    - typing_extensions==4.13.2
    - tzdata==2025.2
    - ultralytics==8.0.85
    - urllib3==2.2.3
    - wcwidth==0.2.13
    - werkzeug==3.0.6
    - yapf==0.30.0
    - zipp==3.20.2
prefix: /media/rhs/t16/anaconda3/envs/RKNN-Toolkit2

