# Qt项目配置 - Ubuntu WSL2环境（兼容版本）
QT       += core gui widgets
TARGET   = ModelConverter
CONFIG   += c++17

# 禁用 sized-deallocation
QMAKE_CXXFLAGS += -fno-sized-deallocation

# 条件编译配置
# 开发模式：qmake CONFIG+=dev_mode（使用简化验证）
# 生产模式：qmake CONFIG+=production（使用完整许可证验证）
dev_mode {
    DEFINES += DEV_MODE_LICENSE
    message("编译模式: 开发模式 - 使用简化许可证验证")
} else:production {
    DEFINES += SF_CORE_LS_EXPORT=Q_DECL_IMPORT
    DEFINES += PRODUCTION_LICENSE
    LIBS += -L$$PWD -lsf-core-ls
    message("编译模式: 生产模式 - 使用完整许可证验证")
} else {
    # 默认开发模式（兼容你当前的环境）
    DEFINES += DEV_MODE_LICENSE
    message("编译模式: 默认开发模式 - 使用简化许可证验证")
}

# 设置运行时 rpath
QMAKE_RPATHDIR += $$PWD

# 源码和头文件
SOURCES  += main.cpp \
            activationwindow.cpp \
            conversionwindow.cpp \
            sized_delete.cpp

HEADERS  += activationwindow.h \
            conversionwindow.h
