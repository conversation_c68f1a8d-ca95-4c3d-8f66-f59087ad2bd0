---
description: Explore and contribute to Ultralytics' utils/patches.py. Learn about the imread, imwrite, imshow, and torch_save functions.
keywords: Ultralytics, utils, patches, imread, imwrite, imshow, torch_save, OpenCV, PyTorch, GitHub
---

# Reference for `ultralytics/utils/patches.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/patches.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/patches.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/patches.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.patches.imread

<br><br><hr><br>

## ::: ultralytics.utils.patches.imwrite

<br><br><hr><br>

## ::: ultralytics.utils.patches.imshow

<br><br><hr><br>

## ::: ultralytics.utils.patches.torch_load

<br><br><hr><br>

## ::: ultralytics.utils.patches.torch_save

<br><br>
