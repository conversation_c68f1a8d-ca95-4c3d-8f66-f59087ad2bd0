---
description: Explore the utility functions for matching in trackers used by Ultralytics, including linear assignment, IoU distance, embedding distance, and more.
keywords: Ultralytics, matching utils, linear assignment, IoU distance, embedding distance, fuse score, tracking, Python, documentation
---

# Reference for `ultralytics/trackers/utils/matching.py`

!!! Note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/utils/matching.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/utils/matching.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/trackers/utils/matching.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.trackers.utils.matching.linear_assignment

<br><br><hr><br>

## ::: ultralytics.trackers.utils.matching.iou_distance

<br><br><hr><br>

## ::: ultralytics.trackers.utils.matching.embedding_distance

<br><br><hr><br>

## ::: ultralytics.trackers.utils.matching.fuse_score

<br><br>
