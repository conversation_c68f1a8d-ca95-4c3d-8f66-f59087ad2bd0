#!/bin/bash
# Qt应用程序智能编译脚本
# 支持开发模式和生产模式的条件编译

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示帮助信息
show_help() {
    echo "Qt应用程序编译脚本"
    echo ""
    echo "用法: $0 [模式] [选项]"
    echo ""
    echo "模式:"
    echo "  dev        开发模式（默认）- 使用简化许可证验证，兼容Qt 5.12.8"
    echo "  production 生产模式 - 使用完整许可证验证，需要sf-core-ls库"
    echo ""
    echo "选项:"
    echo "  --clean    清理之前的编译文件"
    echo "  --help     显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 开发模式编译"
    echo "  $0 dev --clean        # 清理后开发模式编译"
    echo "  $0 production         # 生产模式编译"
}

# 默认参数
BUILD_MODE="dev"
CLEAN_BUILD=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        dev|development)
            BUILD_MODE="dev"
            shift
            ;;
        prod|production)
            BUILD_MODE="production"
            shift
            ;;
        --clean)
            CLEAN_BUILD=true
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        *)
            echo_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

echo_info "=== Qt应用程序编译脚本 ==="
echo_info "编译模式: $BUILD_MODE"

# 检查基础环境
echo_info "1. 检查编译环境..."

if ! command -v qmake &> /dev/null; then
    echo_error "未找到qmake，请安装Qt5开发环境"
    echo_info "运行以下命令安装Qt5："
    echo_info "sudo apt install -y qtbase5-dev qtchooser qt5-qmake qtbase5-dev-tools"
    exit 1
fi

echo_info "Qt版本: $(qmake --version | grep Qt)"

# 进入项目目录
cd autobit/ModelConverter

# 清理编译文件（如果需要）
if [ "$CLEAN_BUILD" = true ]; then
    echo_info "2. 清理之前的编译文件..."
    rm -rf build/
    rm -f Makefile
    rm -f moc_*.cpp
    rm -f qrc_*.cpp
    echo_success "清理完成"
fi

# 创建build目录
mkdir -p build
cd build

# 根据模式设置编译参数
echo_info "3. 配置编译参数..."

if [ "$BUILD_MODE" = "production" ]; then
    echo_info "生产模式编译 - 启用完整许可证验证"
    
    # 检查许可证库是否存在
    if [ ! -f "../libsf-core-ls.so" ]; then
        echo_error "生产模式需要许可证库文件: libsf-core-ls.so"
        echo_info "请确保 libsf-core-ls.so 文件在 autobit/ModelConverter/ 目录中"
        exit 1
    fi
    
    # 检查许可证头文件
    if [ ! -f "../SFCoreIntf.h" ] || [ ! -f "../YXPermission.h" ]; then
        echo_error "生产模式需要许可证头文件: SFCoreIntf.h 和 YXPermission.h"
        exit 1
    fi
    
    QMAKE_CONFIG="CONFIG+=production"
    echo_warning "注意：生产模式需要网络连接到许可证服务器"
    
else
    echo_info "开发模式编译 - 使用简化许可证验证"
    QMAKE_CONFIG="CONFIG+=dev_mode"
    echo_warning "注意：这是开发模式，仅用于开发调试"
fi

# 执行qmake
echo_info "4. 生成Makefile..."
qmake ../ModelConverter.pro $QMAKE_CONFIG

if [ $? -ne 0 ]; then
    echo_error "qmake执行失败"
    exit 1
fi

# 编译项目
echo_info "5. 编译项目..."
make -j$(nproc)

if [ $? -eq 0 ]; then
    echo_success "编译成功！"
    
    # 复制必要的文件
    if [ "$BUILD_MODE" = "production" ] && [ -f "../libsf-core-ls.so" ]; then
        echo_info "复制许可证库文件..."
        cp ../libsf-core-ls.so .
    fi
    
    # 显示编译结果
    echo_info "=== 编译结果 ==="
    echo_info "可执行文件: $(pwd)/ModelConverter"
    echo_info "编译模式: $BUILD_MODE"
    
    if [ "$BUILD_MODE" = "dev" ]; then
        echo_warning "开发模式激活码要求: 10位以上且包含'-'"
        echo_info "测试激活码示例: E76G-JEQR-EQRA-T7ZW"
    fi
    
    echo_info "运行程序: cd $(pwd) && ./ModelConverter"
    
else
    echo_error "编译失败"
    exit 1
fi
