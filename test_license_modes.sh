#!/bin/bash
# 许可证模式测试脚本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo_info "=== 许可证模式兼容性测试 ==="

# 1. 测试开发模式编译
echo_info "1. 测试开发模式编译..."
if ./build_qt_app.sh dev --clean; then
    echo_success "开发模式编译成功"
    DEV_BUILD_OK=true
else
    echo_error "开发模式编译失败"
    DEV_BUILD_OK=false
fi

# 2. 检查开发模式可执行文件
if [ "$DEV_BUILD_OK" = true ]; then
    echo_info "2. 检查开发模式可执行文件..."
    if [ -f "autobit/ModelConverter/build/ModelConverter" ]; then
        echo_success "开发模式可执行文件存在"
        
        # 检查依赖
        echo_info "检查开发模式依赖..."
        cd autobit/ModelConverter/build
        if ldd ./ModelConverter | grep -q "not found"; then
            echo_warning "发现缺失的依赖库"
            ldd ./ModelConverter | grep "not found"
        else
            echo_success "所有依赖库都可用"
        fi
        cd ../../..
    else
        echo_error "开发模式可执行文件不存在"
    fi
fi

# 3. 测试生产模式编译（如果许可证库存在）
echo_info "3. 测试生产模式编译..."
if [ -f "autobit/ModelConverter/libsf-core-ls.so" ]; then
    echo_info "发现许可证库，测试生产模式编译..."
    if ./build_qt_app.sh production --clean; then
        echo_success "生产模式编译成功"
        PROD_BUILD_OK=true
    else
        echo_error "生产模式编译失败"
        PROD_BUILD_OK=false
    fi
else
    echo_warning "未找到许可证库文件，跳过生产模式测试"
    PROD_BUILD_OK="skipped"
fi

# 4. 显示测试结果
echo_info "=== 测试结果汇总 ==="
echo "开发模式编译: $([ "$DEV_BUILD_OK" = true ] && echo "✅ 成功" || echo "❌ 失败")"
echo "生产模式编译: $([ "$PROD_BUILD_OK" = true ] && echo "✅ 成功" || [ "$PROD_BUILD_OK" = "skipped" ] && echo "⏭️ 跳过" || echo "❌ 失败")"

# 5. 提供使用建议
echo_info "=== 使用建议 ==="
if [ "$DEV_BUILD_OK" = true ]; then
    echo_success "✅ 你的环境支持开发模式"
    echo_info "运行命令: ./terminal_command.sh"
    echo_info "测试激活码: E76G-JEQR-EQRA-T7ZW"
fi

if [ "$PROD_BUILD_OK" = true ]; then
    echo_success "✅ 你的环境支持生产模式"
    echo_warning "⚠️  生产模式需要网络连接到许可证服务器"
elif [ "$PROD_BUILD_OK" = "skipped" ]; then
    echo_info "ℹ️  生产模式需要许可证库文件"
    echo_info "如需生产模式，请确保以下文件存在："
    echo_info "  - autobit/ModelConverter/libsf-core-ls.so"
    echo_info "  - autobit/ModelConverter/SFCoreIntf.h"
    echo_info "  - autobit/ModelConverter/YXPermission.h"
fi

echo_info "测试完成。"
