# 部署相关：

## step1：加载镜像文件
docker load -i model-converter.tar

## step2：运行 Qt 程序（Qt版本要求：5.12.6）
cd ModelConverter/build
export LD_LIBRARY_PATH=/home/<USER>/qt512/5.12.6/gcc_64/lib:$LD_LIBRARY_PATH （换成你的路径）
./ModelConverter

## step3：测试用激活参数
deviceNo = "50bf70582c5ea2ac7502656f8cfb522e" 
productNo = "40201"
licenseNo = "E76G-JEQR-EQRA-T7ZW"

## step4：输出内容
转换前的模保存在：ModelConverter/build/models；
转换后的模型保存到：ModelConverter/build/result；
转换log：ModelConverter/build/result
---------------------------------------------------------------------------------------------------------

# docker相关：

## yolo转换Dockerfile：Dockerfile
## yolo转换相关代码库：yolo-rk3588

## 进入docker测试
docker run -it --rm --entrypoint bash model-converter:latest
bash ./convert_model.sh --model yolov8 --weight models/yolov8n.pt --dataset datasets/COCO --output result

## 直接测试
docker run --rm -it \
  -v /media/rhs/t161/autobit/yolo-rk3588:/workspace/yolo-rk3588 \
  -w /workspace/yolo-rk3588 \
  model-converter:latest \
  --model yolov8 --weight models/yolov8n.pt --dataset datasets/COCO --output result


