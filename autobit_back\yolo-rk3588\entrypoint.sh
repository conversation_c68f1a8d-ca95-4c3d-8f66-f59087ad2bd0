#!/usr/bin/env bash
set -euo pipefail

# 1. 加载 Conda 环境
if [ -f "/opt/conda/etc/profile.d/conda.sh" ]; then
  source "/opt/conda/etc/profile.d/conda.sh"
  conda activate RKNN-Toolkit2
else
  echo "❌ 无法找到 Conda 安装路径，请检查 CONDA_DIR 是否正确。"
  exit 1
fi

# 2. 切换到脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 3. 转发所有参数给 convert_model.sh
if [ $# -gt 0 ]; then
  echo "🚀 执行 convert_model.sh $*"
  exec bash "./convert_model.sh" "$@"
else
  echo "❗ 未检测到参数，请传入 --model、--weight 等参数进行转换。"
  exit 1
fi
