#ifndef CONVERSIONWINDOW_H
#define CONVERSIONWINDOW_H

#include <QWidget>
#include <QProcess>
#include <QComboBox>
#include <QLineEdit>
#include <QPushButton>
#include <QTextEdit>
#include <QLabel>

class ConversionWindow : public QWidget
{
    Q_OBJECT

public:
    explicit ConversionWindow(QWidget *parent = nullptr);

private slots:
    void onBrowseClicked();
    void onConvertClicked();
    void onProcessOutput();
    void onProcessErrorOutput();
    void onProcessFinished(int exitCode);

private:
    void setupUI();
    void setupConnections();

    QComboBox   *modelTypeCombo;
    QLineEdit   *modelPathEdit;
    QPushButton *browseButton;
    QPushButton *convertButton;
    QTextEdit   *resultDisplay;
    QLabel      *statusLabel;
    QLabel      *spinnerLabel;
    QProcess    *process;
};

#endif