# docker环境创建及配置
- 安装rknn-toolkit2
`docker load --input ./tools/rknn-toolkit2-v2.3.0-cp38-docker.tar.gz`

- 进入docker环境，将yolo-rk3588文件夹映射到/home/<USER>/yolo-rk3588

  - 若服务器不带cuda:
  `docker run -t -i --privileged -v /home/<USER>/Codes/autobit/yolo-rk3588:/home/<USER>/yolo-rk3588 --shm-size 8G rknn-toolkit2:2.3.0-cp38 /bin/bash`
<!-- `docker run -t -i --privileged -v /home/<USER>/Codes/autobit/yolo-rk3588:/home/<USER>/yolo-rk3588 -v /mnt/data/sata/ssd/hcy/datasets/coco:/home/<USER>/coco --shm-size 8G rknn-toolkit2:2.3.0-cp38 /bin/bash` -->

  - 若服务器带cuda(需要安装NVIDIA Container Toolkit):
  `docker run -t -i --gpus all --privileged -v /home/<USER>/Codes/autobit/yolo-rk3588:/home/<USER>/yolo-rk3588 -v /home/<USER>/Datasets/COCO:/home/<USER>/yolo-rk3588/datasets/COCO2017 --shm-size 16G nvcr.io/nvidia/cuda:12.2.2-cudnn8-devel-ubuntu20.04 /bin/bash`
<!-- `docker run -t -i --gpus all --privileged -v /home/<USER>/Codes/autobit/yolo-rk3588:/home/<USER>/yolo-rk3588 --shm-size 8G rknn-toolkit2:2.3.0-cp38 /bin/bash` -->
<!-- `docker run -t -i --gpus all --privileged -v /home/<USER>/Codes/autobit/yolo-rk3588:/home/<USER>/yolo-rk3588 -v /mnt/data/sata/ssd/hcy/datasets/coco:/home/<USER>/coco --shm-size 8G rknn-toolkit2:2.3.0-cp38 /bin/bash` -->

- 更新软件包
`apt update`

- 安装python环境(期间会出现选择地区，选择2.America和107.New_York)
`apt-get install python3 python3-dev python3-pip`
`apt-get install libxslt1-dev zlib1g zlib1g-dev libglib2.0-0 libsm6 libgl1-mesa-glx libprotobuf-dev gcc`

- 安装wget、git、cmake、ssh
`apt install wget git cmake openssh-server`

- 安装Miniforge(如果提前下好了就不用wget)
`wget -c https://github.com/conda-forge/miniforge/releases/latest/download/Miniforge3-Linux-x86_64.sh`
`chmod 777 Miniforge3-Linux-x86_64.sh`
`bash Miniforge3-Linux-x86_64.sh`

- 进入conda base环境
`source ~/miniforge3/bin/activate`

- 创建conda环境
`conda create -n RKNN-Toolkit2 python=3.8`

- 进入conda环境
`conda activate RKNN-Toolkit2`

- 安装rknn-toolkit2
`pip install rknn-toolkit2 -i https://pypi.tuna.tsinghua.edu.cn/simple`

<!-- - 安装git
`apt install git` -->

- 安装依赖库
`cd /home/<USER>/yolo-rk3588`
`pip3 install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple`
<!-- - 安装yolov5库
`pip3 install ultralytics==8.0.85 -i https://pypi.tuna.tsinghua.edu.cn/simple`
- 安装剪枝库
`pip3 install torch_pruning==1.5.1` -->

<!-- - 更新软件包
`apt update`

- 安装cmake
`apt install cmake`

- 安装ssh服务
`apt-get install openssh-server` -->

- 解压交叉编译
`tar -xvf ./tools/gcc-linaro-6.3.1-2017.05-x86_64_aarch64-linux-gnu.tar`

# 模型量化与转换(docker中运行)

## yolov5
- 将.pt模型转换为.onnx模型(--weight: 输入模型.pt文件路径; --result: 保存的.onnx文件路径)
`python yolov5/export.py --rknpu --weight models/yolov5s.pt --result result/yolov5/yolov5s.onnx`

- 将onnx模型量化并转换为rknn模型(conver.py包含5个参数: 1.读取onnx文件路径; 2.rk3588平台; 3.i8表示int8量化; 4.保存rknn文件路径; 5.数据集路径)
`python yolov5/convert.py result/yolov5/yolov5s.onnx rk3588 i8 result/yolov5/yolov5s.rknn datasets/COCO/coco_subset_20.txt`

- 编译(-i: 上一步输出rknn文件的路径; -o: 输出文件夹的路径)
`./build-linux.sh -t rk3588 -a aarch64 -d yolov5 -i /home/<USER>/yolo-rk3588/result/yolov5 -o /home/<USER>/yolo-rk3588/yolov5/deploy`

- 生成文件夹 `/home/<USER>/yolo-rk3588/yolov5/deploy`, 其中`install/rk3588_linux_aarch64/rknn_yolov5_demo`需要发送到rk3588设备

<!-- # 模型量化(docker中运行)
- 执行量化,对.pt模型进行量化，得到.onnx模型
`python main.py --arch yolov5s --config ./exp/w8a8/yolov5s.yaml --model_load_path /home/<USER>/yolo-rk3588/model/yolov5s.pt`

# 模型转换(docker中运行)
- 将onnx模型转换为rknn模型
`python convert.py ./model/yolov5su.onnx rk3588 i8 ./model/yolov5su.rknn`
- 编译
`` -->

## yolov8
- 将.pt模型转换为.onnx模型(--weight: 输入模型.pt文件路径; --result: 保存的.onnx文件路径)
`export PYTHONPATH=./yolov8`
`python yolov8/ultralytics/engine/exporter.py --weight models/yolov8n.pt --result result/yolov8/yolov8n.onnx`

- 将onnx模型量化并转换为rknn模型(conver.py包含5个参数: 1.读取onnx文件路径; 2.rk3588平台; 3.i8表示int8量化; 4.保存rknn文件路径; 5.数据集路径)
`python yolov8/convert.py result/yolov8/yolov8n.onnx rk3588 i8 result/yolov8/yolov8n.rknn datasets/COCO/coco_subset_20.txt`

- 编译(-i: 上一步输出rknn文件的路径; -o: 输出文件夹的路径)
`./build-linux.sh -t rk3588 -a aarch64 -d yolov8 -i /home/<USER>/yolo-rk3588/result/yolov8 -o /home/<USER>/yolo-rk3588/yolov8/deploy`

- 生成文件夹 `/home/<USER>/yolo-rk3588/yolov8/deploy`, 其中`install/rk3588_linux_aarch64/rknn_yolov8_demo`需要发送到rk3588设备

# 模型推理(rk3588上运行)

## yolov5

- 将生成的`rknn_yolov5_demo`发送到rk3588设备(password: `orangepi`)
`scp -r -P 11063 /home/<USER>/yolo-rk3588/yolov5/deploy/install/rk3588_linux_aarch64/rknn_yolov5_demo <EMAIL>:/home/<USER>/Codes`

- 登录rk3588设备(password: `orangepi`)
`ssh <EMAIL> -p 11063`

- 进入`rknn_yolov5_demo`文件
`cd ./Codes/rknn_yolov5_demo`

- 执行推理生成图片`out.png`
 `export LD_LIBRARY_PATH=./lib`
 ` ./rknn_yolov5_demo model/yolov5s.rknn model/bus.jpg`

- 执行评测, 返回`mAP`和推理速度
`export LD_LIBRARY_PATH=./lib`
`./rknn_yolov5_demo model/yolov5s.rknn ../Datasets/coco128/images/ ../Datasets/coco128/labels/`

## yolov8

- 将生成的`rknn_yolov8_demo`发送到rk3588设备(password: `orangepi`)
`scp -r -P 11063 /home/<USER>/yolo-rk3588/yolov8/deploy/install/rk3588_linux_aarch64/rknn_yolov8_demo <EMAIL>:/home/<USER>/Codes`

- 登录rk3588设备(password: `orangepi`)
`ssh <EMAIL> -p 11063`

- 进入`rknn_yolov8_demo`文件
`cd ./Codes/rknn_yolov8_demo`

- 执行推理生成图片`out.png`
 `export LD_LIBRARY_PATH=./lib`
 ` ./rknn_yolov8_demo model/yolov8n.rknn model/bus.jpg`

- 执行评测, 返回`mAP`和推理速度
`export LD_LIBRARY_PATH=./lib`
`./rknn_yolov8_demo model/yolov8n.rknn ../Datasets/coco128/images/ ../Datasets/coco128/labels/`


# start_quant.sh

## yolov5
`./start_quant.sh --model_name yolov5s --model_weight_path models/yolov5s.pt --model_py_path /path/to/yolov5.py --dataset_path datasets/COCO --result_model_path result/yolov5`

## yolov8
`./start_quant.sh --model_name yolov8n --model_weight_path models/yolov8n.pt --model_py_path /path/to/yolov8.py --dataset_path datasets/COCO --result_model_path result/yolov8`

