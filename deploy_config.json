{"deployment": {"name": "YOLO模型转换工具", "version": "1.0.0", "description": "基于Qt的YOLO模型转换工具，支持开发和生产模式"}, "build_modes": {"development": {"description": "开发模式 - 适用于本地开发环境", "license_mode": "simplified", "qt_version_requirement": "5.12.x", "dependencies": ["qtbase5-dev", "<PERSON><PERSON><PERSON><PERSON>", "qt5-qmake", "qtbase5-dev-tools"], "security_level": "low", "activation_requirements": "10位以上且包含'-'的字符串", "network_required": false}, "production": {"description": "生产模式 - 适用于设备部署", "license_mode": "full_verification", "qt_version_requirement": "5.12.6", "dependencies": ["qtbase5-dev", "libsf-core-ls.so", "SFCoreIntf.h", "YXPermission.h"], "security_level": "high", "activation_requirements": "有效的许可证服务器验证", "network_required": true, "license_server": "api.shifang.co"}}, "security_considerations": {"development_mode_warnings": ["仅用于开发调试，不可用于生产环境", "使用硬编码的模拟设备号", "激活验证极其简化，存在安全风险", "任何符合格式的字符串都能通过验证"], "production_mode_features": ["真实设备指纹识别", "网络许可证服务器验证", "加密的设备信息传输", "完整的错误处理和日志记录"], "deployment_checklist": ["确认编译模式为production", "验证许可证库文件完整性", "测试网络连接到许可证服务器", "确认设备号生成正常", "验证激活流程完整性"]}, "environment_compatibility": {"supported_platforms": ["Ubuntu 20.04 WSL2", "Ubuntu 18.04+", "CentOS 7+", "Debian 10+"], "qt_versions": {"development": "5.12.8 (系统包管理器安装)", "production": "5.12.6 (推荐) 或 5.12.x 兼容版本"}, "known_issues": ["Qt版本不匹配可能导致编译失败", "WSL2环境下需要X11转发支持", "许可证库依赖特定的glibc版本"]}, "build_commands": {"development": {"clean_build": "./build_qt_app.sh dev --clean", "incremental_build": "./build_qt_app.sh dev", "run_tests": "./terminal_command.sh"}, "production": {"clean_build": "./build_qt_app.sh production --clean", "incremental_build": "./build_qt_app.sh production", "deploy_package": "tar -czf ModelConverter-production.tar.gz build/"}}, "troubleshooting": {"common_errors": {"qt_version_mismatch": {"error": "编译时Qt版本不匹配", "solution": "使用条件编译，开发模式兼容系统Qt版本"}, "missing_license_lib": {"error": "找不到libsf-core-ls.so", "solution": "确保许可证库文件在正确位置，或使用开发模式"}, "x11_display_error": {"error": "GUI无法显示", "solution": "配置X11转发，设置DISPLAY环境变量"}}}}